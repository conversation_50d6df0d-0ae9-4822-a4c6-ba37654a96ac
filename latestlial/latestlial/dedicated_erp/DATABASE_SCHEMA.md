# مخطط قاعدة البيانات - نظام متبرمج
## Database Schema - Mutabarramij ERP System

### 🗄️ الجداول الأساسية / Core Tables

## 1. جداول المستخدمين والصلاحيات / Users & Permissions

### `users` - المستخدمين
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    name_ar VARCHAR(255) NOT NULL COMMENT 'الاسم بالعربية',
    name_en VARCHAR(255) NULL COMMENT 'الاسم بالإنجليزية',
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) NULL,
    avatar VARCHAR(255) NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_name_ar (name_ar),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `roles` - الأدوار
```sql
CREATE TABLE roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name_ar VARCHAR(255) NOT NULL,
    display_name_en VARCHAR(255) NULL,
    description_ar TEXT NULL,
    description_en TEXT NULL,
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `permissions` - الصلاحيات
```sql
CREATE TABLE permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name_ar VARCHAR(255) NOT NULL,
    display_name_en VARCHAR(255) NULL,
    module VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `user_roles` - ربط المستخدمين بالأدوار
```sql
CREATE TABLE user_roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    role_id BIGINT UNSIGNED NOT NULL,
    assigned_by BIGINT UNSIGNED NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_role (user_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 2. جداول العملاء / Clients

### `clients` - العملاء
```sql
CREATE TABLE clients (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    company_name_ar VARCHAR(255) NOT NULL,
    company_name_en VARCHAR(255) NULL,
    client_code VARCHAR(50) UNIQUE NOT NULL,
    industry VARCHAR(100) NULL,
    website VARCHAR(255) NULL,
    logo VARCHAR(255) NULL,
    address_ar TEXT NULL,
    address_en TEXT NULL,
    city VARCHAR(100) NULL,
    country VARCHAR(100) DEFAULT 'مصر',
    postal_code VARCHAR(20) NULL,
    phone VARCHAR(20) NULL,
    email VARCHAR(255) NULL,
    tax_number VARCHAR(50) NULL,
    commercial_register VARCHAR(50) NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    notes_ar TEXT NULL,
    notes_en TEXT NULL,
    created_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_client_code (client_code),
    INDEX idx_company_name_ar (company_name_ar),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `client_contacts` - جهات اتصال العملاء
```sql
CREATE TABLE client_contacts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NULL,
    position_ar VARCHAR(100) NULL,
    position_en VARCHAR(100) NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    mobile VARCHAR(20) NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_client_id (client_id),
    INDEX idx_is_primary (is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 3. جداول المشاريع / Projects

### `projects` - المشاريع
```sql
CREATE TABLE projects (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    project_code VARCHAR(50) UNIQUE NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NULL,
    description_ar TEXT NULL,
    description_en TEXT NULL,
    client_id BIGINT UNSIGNED NOT NULL,
    project_manager_id BIGINT UNSIGNED NULL,
    lead_developer_id BIGINT UNSIGNED NULL,
    project_type ENUM('website', 'mobile_app', 'desktop_app', 'system', 'other') NOT NULL,
    status ENUM('planning', 'in_progress', 'testing', 'completed', 'cancelled', 'on_hold') DEFAULT 'planning',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    start_date DATE NULL,
    end_date DATE NULL,
    estimated_hours DECIMAL(8,2) NULL,
    actual_hours DECIMAL(8,2) DEFAULT 0,
    budget DECIMAL(12,2) NULL,
    currency VARCHAR(3) DEFAULT 'EGP',
    progress_percentage TINYINT UNSIGNED DEFAULT 0,
    created_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE RESTRICT,
    FOREIGN KEY (project_manager_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (lead_developer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_project_code (project_code),
    INDEX idx_client_id (client_id),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `project_phases` - مراحل المشاريع
```sql
CREATE TABLE project_phases (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT UNSIGNED NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NULL,
    description_ar TEXT NULL,
    description_en TEXT NULL,
    phase_order TINYINT UNSIGNED NOT NULL,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    start_date DATE NULL,
    end_date DATE NULL,
    estimated_hours DECIMAL(8,2) NULL,
    actual_hours DECIMAL(8,2) DEFAULT 0,
    progress_percentage TINYINT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project_id (project_id),
    INDEX idx_phase_order (phase_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `tasks` - المهام
```sql
CREATE TABLE tasks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    project_id BIGINT UNSIGNED NOT NULL,
    phase_id BIGINT UNSIGNED NULL,
    parent_task_id BIGINT UNSIGNED NULL,
    title_ar VARCHAR(255) NOT NULL,
    title_en VARCHAR(255) NULL,
    description_ar TEXT NULL,
    description_en TEXT NULL,
    assigned_to BIGINT UNSIGNED NULL,
    assigned_by BIGINT UNSIGNED NULL,
    task_type ENUM('development', 'design', 'testing', 'documentation', 'meeting', 'other') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('todo', 'in_progress', 'review', 'completed', 'cancelled') DEFAULT 'todo',
    estimated_hours DECIMAL(6,2) NULL,
    actual_hours DECIMAL(6,2) DEFAULT 0,
    start_date DATE NULL,
    due_date DATE NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (phase_id) REFERENCES project_phases(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_project_id (project_id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 4. جداول تتبع الوقت / Time Tracking

### `time_entries` - إدخالات الوقت
```sql
CREATE TABLE time_entries (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    project_id BIGINT UNSIGNED NOT NULL,
    task_id BIGINT UNSIGNED NULL,
    description_ar TEXT NULL,
    description_en TEXT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NULL,
    duration_minutes INT UNSIGNED NULL,
    is_billable BOOLEAN DEFAULT TRUE,
    hourly_rate DECIMAL(8,2) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 5. الجداول المالية / Financial Tables

### `invoices` - الفواتير
```sql
CREATE TABLE invoices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) UNIQUE NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    client_id BIGINT UNSIGNED NOT NULL,
    project_id BIGINT UNSIGNED NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 14.00,
    tax_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'EGP',
    notes_ar TEXT NULL,
    notes_en TEXT NULL,
    created_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE RESTRICT,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_client_id (client_id),
    INDEX idx_status (status),
    INDEX idx_invoice_date (invoice_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### `invoice_items` - عناصر الفواتير
```sql
CREATE TABLE invoice_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    invoice_id BIGINT UNSIGNED NOT NULL,
    description_ar TEXT NOT NULL,
    description_en TEXT NULL,
    quantity DECIMAL(8,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 6. فهارس البحث النصي / Full-Text Search Indexes

```sql
-- فهرس البحث في المشاريع
ALTER TABLE projects ADD FULLTEXT(name_ar, description_ar);
ALTER TABLE projects ADD FULLTEXT(name_en, description_en);

-- فهرس البحث في العملاء
ALTER TABLE clients ADD FULLTEXT(company_name_ar, address_ar, notes_ar);

-- فهرس البحث في المهام
ALTER TABLE tasks ADD FULLTEXT(title_ar, description_ar);

-- فهرس البحث في المستخدمين
ALTER TABLE users ADD FULLTEXT(name_ar);
```

## 7. البيانات الأولية / Initial Data

### الأدوار الأساسية
```sql
INSERT INTO roles (name, display_name_ar, display_name_en, is_system_role) VALUES
('super_admin', 'المدير العام', 'Super Admin', TRUE),
('project_manager', 'مدير المشاريع', 'Project Manager', TRUE),
('lead_developer', 'مطور رئيسي', 'Lead Developer', TRUE),
('employee', 'موظف', 'Employee', TRUE),
('client', 'عميل', 'Client', TRUE);
```

### الصلاحيات الأساسية
```sql
INSERT INTO permissions (name, display_name_ar, module, action) VALUES
('projects.view', 'عرض المشاريع', 'projects', 'view'),
('projects.create', 'إنشاء مشروع', 'projects', 'create'),
('projects.edit', 'تعديل المشاريع', 'projects', 'edit'),
('projects.delete', 'حذف المشاريع', 'projects', 'delete'),
('clients.view', 'عرض العملاء', 'clients', 'view'),
('clients.create', 'إنشاء عميل', 'clients', 'create'),
('clients.edit', 'تعديل العملاء', 'clients', 'edit'),
('clients.delete', 'حذف العملاء', 'clients', 'delete');
```

## 8. إعدادات الأداء / Performance Settings

```sql
-- تحسين إعدادات MySQL للنصوص العربية
SET GLOBAL innodb_ft_min_token_size = 1;
SET GLOBAL ft_min_word_len = 1;

-- إعدادات الذاكرة
SET GLOBAL innodb_buffer_pool_size = 1G;
SET GLOBAL query_cache_size = 256M;
```

---

*مخطط قاعدة البيانات لنظام متبرمج ERP - تم تصميمه لدعم اللغة العربية والأداء العالي*
