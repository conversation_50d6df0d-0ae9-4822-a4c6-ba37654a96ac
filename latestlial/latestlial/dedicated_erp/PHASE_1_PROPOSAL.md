# نظام متبرمج - مقترح المرحلة الأولى
## Mutabarramij ERP System - Phase 1 Comprehensive Proposal

### 📋 نظرة عامة على المشروع / Project Overview

**اسم النظام:** مت<PERSON><PERSON>مج (Mutabarramij) - نظام تخطيط موارد المؤسسات  
**النوع:** نظام ERP شامل للوكالات البرمجية  
**اللغة الأساسية:** العربية (RTL) مع دعم الإنجليزية  
**التقنيات:** Laravel 10+, MySQL 8.0+, Responsive CSS3, Vanilla JavaScript  

---

## 🏗️ 1. هيكل إدارة المستخدمين / User Management Structure

### الأدوار والصلاحيات / Roles & Permissions

#### 1.1 الأدوار المحددة (5 أدوار رئيسية):

**1. المدير العام (Super Admin)**
- الصلاحيات: وصول كامل لجميع الوحدات
- المسؤوليات: إدارة النظام، إعداد الشركة، إدارة المستخدمين

**2. مدير المشاريع (Project Manager)**  
- الصلاحيات: إدارة المشاريع، العملاء، الفرق، التقارير
- المسؤوليات: تخطيط وتنفيذ المشاريع، متابعة الأداء

**3. مطور رئيسي (Lead Developer)**
- الصلاحيات: إدارة المهام التقنية، مراجعة الكود، إدارة الفريق التقني
- المسؤوليات: الإشراف التقني، ضمان الجودة

**4. موظف (Employee)**
- الصلاحيات: تسجيل الوقت، إدارة المهام المخصصة، رفع التقارير
- المسؤوليات: تنفيذ المهام، تحديث حالة العمل

**5. عميل (Client)**
- الصلاحيات: عرض المشاريع الخاصة، متابعة التقدم، التواصل
- المسؤوليات: مراجعة التسليمات، تقديم التغذية الراجعة

#### 1.2 مصفوفة الصلاحيات:

| الوحدة | المدير العام | مدير المشاريع | مطور رئيسي | موظف | عميل |
|---------|-------------|--------------|------------|-------|-------|
| إدارة المشاريع | ✅ كامل | ✅ كامل | ✅ قراءة/تعديل | ✅ قراءة | ✅ مشاريعه فقط |
| إدارة العملاء | ✅ كامل | ✅ كامل | ❌ | ❌ | ❌ |
| الإدارة المالية | ✅ كامل | ✅ قراءة | ❌ | ❌ | ✅ فواتيره فقط |
| الموارد البشرية | ✅ كامل | ✅ قراءة | ❌ | ✅ بياناته فقط | ❌ |
| إدارة المخزون | ✅ كامل | ✅ قراءة/تعديل | ✅ قراءة | ❌ | ❌ |
| إدارة الوثائق | ✅ كامل | ✅ كامل | ✅ قراءة/تعديل | ✅ قراءة | ✅ وثائقه فقط |
| التقارير | ✅ كامل | ✅ كامل | ✅ تقاريره | ✅ تقاريره | ✅ تقاريره |
| الإعدادات | ✅ كامل | ❌ | ❌ | ❌ | ❌ |

---

## 🏢 2. هيكل وحدات النظام / ERP Module Architecture

### 2.1 الوحدات الأساسية (12 وحدة):

**1. إدارة المشاريع (Project Management)**
- تخطيط المشاريع والمراحل
- تتبع التقدم والمهام
- إدارة الفرق والموارد
- جدولة زمنية تفاعلية

**2. إدارة العملاء (Client Management)**
- قاعدة بيانات العملاء
- تاريخ التعاملات
- إدارة العقود والاتفاقيات
- نظام CRM متكامل

**3. الإدارة المالية (Financial Management)**
- الفواتير والمدفوعات
- المحاسبة والميزانية
- تتبع الأرباح والخسائر
- التقارير المالية

**4. الموارد البشرية (Human Resources)**
- إدارة الموظفين
- الحضور والانصراف
- الرواتب والمكافآت
- تقييم الأداء

**5. إدارة المخزون (Inventory Management)**
- الأجهزة والمعدات
- البرمجيات والتراخيص
- طلبات الشراء
- تتبع الأصول

**6. إدارة الوثائق (Document Management)**
- تخزين الملفات المنظم
- نظام الأرشفة
- مشاركة الوثائق
- التحكم في الإصدارات

**7. التقارير والتحليلات (Reports & Analytics)**
- لوحات المعلومات التفاعلية
- تقارير مخصصة
- تحليل الأداء
- مؤشرات الأداء الرئيسية

**8. إدارة المهام (Task Management)**
- تخصيص المهام
- تتبع الوقت
- إدارة الأولويات
- تقارير الإنتاجية

**9. إدارة الجودة (Quality Management)**
- معايير الجودة
- مراجعة الكود
- اختبار المشاريع
- ضمان الجودة

**10. التواصل والإشعارات (Communication Hub)**
- نظام الرسائل الداخلية
- الإشعارات الفورية
- التقويم المشترك
- إدارة الاجتماعات

**11. إدارة المعرفة (Knowledge Management)**
- قاعدة المعرفة
- الوثائق التقنية
- أفضل الممارسات
- التدريب والتطوير

**12. الإعدادات والتكوين (Settings & Configuration)**
- إعدادات النظام
- إدارة الصلاحيات
- التخصيص والقوالب
- النسخ الاحتياطي

### 2.2 العلاقات بين الوحدات:

```
إدارة المشاريع ←→ إدارة العملاء ←→ الإدارة المالية
       ↓                    ↓                    ↓
إدارة المهام ←→ الموارد البشرية ←→ التقارير والتحليلات
       ↓                    ↓                    ↓
إدارة الوثائق ←→ إدارة المخزون ←→ إدارة الجودة
```

---

## 🗄️ 3. تصميم قاعدة البيانات / Database Design

### 3.1 الجداول الأساسية:

**جداول المستخدمين:**
- `users` - بيانات المستخدمين الأساسية
- `roles` - الأدوار والصلاحيات  
- `permissions` - الصلاحيات التفصيلية
- `user_roles` - ربط المستخدمين بالأدوار

**جداول المشاريع:**
- `projects` - بيانات المشاريع
- `project_phases` - مراحل المشاريع
- `tasks` - المهام والأنشطة
- `time_tracking` - تتبع الوقت

**جداول العملاء:**
- `clients` - بيانات العملاء
- `client_contacts` - جهات الاتصال
- `contracts` - العقود والاتفاقيات
- `client_communications` - سجل التواصل

**جداول المالية:**
- `invoices` - الفواتير
- `payments` - المدفوعات
- `expenses` - المصروفات
- `financial_reports` - التقارير المالية

### 3.2 استراتيجية الدعم متعدد اللغات:

- **الحقول النصية:** استخدام JSON لتخزين النصوص بعدة لغات
- **الترميز:** UTF-8 مع utf8mb4_unicode_ci collation
- **الفهرسة:** فهارس خاصة للنصوص العربية
- **البحث:** دعم البحث النصي الكامل باللغة العربية

---

## 📱 4. استراتيجية التصميم المتجاوب / Responsive Design Strategy

### 4.1 نقاط التوقف (Breakpoints):

- **الهاتف المحمول:** < 768px
- **الجهاز اللوحي:** 768px - 1024px  
- **سطح المكتب:** > 1024px

### 4.2 حلول الجداول المتجاوبة:

**أ. التمرير الأفقي مع العمود الثابت:**
```css
.responsive-table {
    overflow-x: auto;
    position: relative;
}

.sticky-column {
    position: sticky;
    right: 0;
    background: white;
    z-index: 10;
}
```

**ب. إخفاء الأعمدة حسب الأولوية:**
```css
@media (max-width: 768px) {
    .column-optional { display: none; }
}

@media (max-width: 480px) {
    .column-important { display: none; }
}
```

**ج. تحويل إلى تخطيط البطاقات:**
```css
@media (max-width: 768px) {
    .table-row {
        display: block;
        border: 1px solid #ddd;
        margin-bottom: 10px;
        padding: 10px;
    }
    
    .table-cell {
        display: block;
        text-align: right;
    }
    
    .table-cell:before {
        content: attr(data-label) ": ";
        font-weight: bold;
    }
}
```

### 4.3 أنماط التفاعل باللمس:

- **الحد الأدنى لحجم اللمس:** 44px × 44px
- **المسافات بين العناصر:** 8px كحد أدنى
- **أزرار الإجراءات:** واضحة ومتباعدة
- **القوائم المنسدلة:** محسنة للمس

---

## 🎨 5. مواصفات تصميم واجهة المستخدم / UI/UX Design Specifications

### 5.1 الألوان والهوية البصرية:

**الألوان الأساسية:**
- **الأزرق الأساسي:** #2563eb (للعناصر الأساسية)
- **الأخضر:** #059669 (للنجاح والموافقة)
- **الأحمر:** #dc2626 (للتحذيرات والأخطاء)
- **الرمادي:** #6b7280 (للنصوص الثانوية)

**الخلفيات:**
- **الخلفية الأساسية:** #f9fafb
- **خلفية البطاقات:** #ffffff
- **الحدود:** #e5e7eb

### 5.2 الخطوط والطباعة:

**الخط الأساسي:** IBM Plex Sans Arabic
```css
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'IBM Plex Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}
```

**التدرج الهرمي:**
- **العناوين الرئيسية:** 2rem (32px) - وزن 700
- **العناوين الفرعية:** 1.5rem (24px) - وزن 600  
- **النص الأساسي:** 1rem (16px) - وزن 400
- **النص الثانوي:** 0.875rem (14px) - وزن 400

### 5.3 الإطارات الشبكية (Wireframes):

**أ. لوحة المعلومات الرئيسية:**
```
┌─────────────────────────────────────────────────────┐
│ شعار متبرمج    [البحث]    [الإشعارات] [المستخدم] │
├─────────────────────────────────────────────────────┤
│ [القائمة الجانبية]  │  [المحتوى الرئيسي]        │
│ • لوحة المعلومات    │  ┌─────────────────────────┐ │
│ • المشاريع          │  │ إحصائيات سريعة         │ │
│ • العملاء           │  │ [4 بطاقات معلومات]     │ │
│ • المالية           │  └─────────────────────────┘ │
│ • الموظفين          │  ┌─────────────────────────┐ │
│                     │  │ الرسوم البيانية        │ │
│                     │  └─────────────────────────┘ │
└─────────────────────────────────────────────────────┘
```

**ب. عرض الجداول المتجاوبة:**
```
سطح المكتب:
┌──────────┬──────────┬──────────┬──────────┬──────────┐
│ الإجراءات │ التاريخ   │ الحالة    │ العميل    │ المشروع   │
├──────────┼──────────┼──────────┼──────────┼──────────┤
│ [تعديل]   │ 2024/01  │ نشط     │ شركة أ    │ موقع ويب  │
└──────────┴──────────┴──────────┴──────────┴──────────┘

الهاتف المحمول:
┌─────────────────────────────────────────────────────┐
│ موقع ويب                                    [تعديل] │
│ العميل: شركة أ                                      │
│ الحالة: نشط                                        │
│ التاريخ: 2024/01                                   │
└─────────────────────────────────────────────────────┘
```

---

## ⚡ 6. المتطلبات التقنية / Technical Requirements

### 6.1 المكدس التقني:

**الخادم الخلفي:**
- Laravel 10.x
- PHP 8.1+
- MySQL 8.0+
- Redis (للتخزين المؤقت)

**الواجهة الأمامية:**
- Blade Templates
- CSS3 (Grid/Flexbox)
- Vanilla JavaScript ES6+
- Alpine.js (للتفاعلات البسيطة)

**الأدوات والمكتبات:**
- Tailwind CSS (مخصص للـ RTL)
- Chart.js (للرسوم البيانية)
- DataTables (للجداول المتقدمة)
- SweetAlert2 (للتنبيهات)

### 6.2 متطلبات الأداء:

- **سرعة التحميل:** < 3 ثواني
- **PageSpeed Insights:** 90+ للهاتف المحمول
- **التحسين:** ضغط الصور، تصغير CSS/JS
- **التخزين المؤقت:** Redis + Browser Caching

### 6.3 الأمان:

- **المصادقة:** Laravel Sanctum
- **الحماية:** CSRF, XSS, SQL Injection
- **التشفير:** HTTPS إجباري
- **النسخ الاحتياطي:** يومي تلقائي

---

## 📊 7. معالجة الشعار / Logo Processing

### 7.1 المتطلبات:

- **إزالة الخلفية:** تحويل إلى PNG شفاف
- **الأحجام المطلوبة:**
  - Favicon: 16x16, 32x32, 48x48
  - Header: 200x60
  - Mobile: 150x45  
  - Print: 300x90 (عالي الدقة)

### 7.2 التحسين:

- **تحسين الحجم:** ضغط بدون فقدان جودة
- **التوافق:** دعم الخلفيات الفاتحة والداكنة
- **التنسيقات:** PNG, SVG, ICO

---

## 📅 8. الجدول الزمني للتنفيذ / Implementation Timeline

### المرحلة الأولى (4 أسابيع):
- **الأسبوع 1:** إعداد البيئة وقاعدة البيانات
- **الأسبوع 2:** نظام المصادقة وإدارة المستخدمين  
- **الأسبوع 3:** الوحدات الأساسية (المشاريع، العملاء)
- **الأسبوع 4:** التصميم المتجاوب والاختبار

### المرحلة الثانية (4 أسابيع):
- **الأسبوع 5-6:** الوحدات المالية والموارد البشرية
- **الأسبوع 7-8:** التقارير والتحليلات

### المرحلة الثالثة (2 أسابيع):
- **الأسبوع 9-10:** التحسين والاختبار النهائي

---

## ✅ 9. معايير النجاح / Success Metrics

- **الجداول:** قابلة للاستخدام على شاشات 320px
- **النصوص العربية:** عرض صحيح مع محاذاة RTL
- **الأداء:** 90+ على PageSpeed Insights للهاتف
- **تجربة المستخدم:** سهولة استخدام للمتحدثين بالعربية
- **الاستجابة:** تفاعل سريع على الأجهزة اللمسية

---

## 🔍 10. نقطة الموافقة / Approval Checkpoint

**هذا المقترح يتطلب موافقة صريحة قبل بدء أي تطوير للكود.**

**المطلوب للموافقة:**
- ✅ مراجعة هيكل الأدوار والصلاحيات
- ✅ تأكيد الوحدات المطلوبة  
- ✅ موافقة على تصميم قاعدة البيانات
- ✅ اعتماد استراتيجية التصميم المتجاوب
- ✅ تأكيد الجدول الزمني

**بعد الموافقة سيتم البدء في:**
1. معالجة الشعار وإنشاء الأحجام المطلوبة
2. إعداد مشروع Laravel مع التكوين الأساسي
3. تطوير نظام المصادقة والأدوار
4. بناء الوحدات حسب الأولوية المحددة

---

*تم إعداد هذا المقترح بواسطة Augment Agent لنظام متبرمج ERP*
