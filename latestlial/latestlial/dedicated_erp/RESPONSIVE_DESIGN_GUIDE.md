# دليل التصميم المتجاوب - نظام متبرمج
## Responsive Design Guide - Mutabarramij ERP System

### 📱 استراتيجية التصميم المتجاوب الشاملة

## 1. نقاط التوقف والشاشات / Breakpoints & Screen Sizes

```css
/* نقاط التوقف الأساسية */
:root {
  --breakpoint-xs: 320px;   /* الهواتف الصغيرة */
  --breakpoint-sm: 576px;   /* الهواتف الكبيرة */
  --breakpoint-md: 768px;   /* الأجهزة اللوحية */
  --breakpoint-lg: 992px;   /* أجهزة سطح المكتب الصغيرة */
  --breakpoint-xl: 1200px;  /* أجهزة سطح المكتب الكبيرة */
  --breakpoint-xxl: 1400px; /* الشاشات الكبيرة جداً */
}

/* Media Queries للتصميم المتجاوب */
@media (max-width: 575.98px) { /* الهواتف */ }
@media (min-width: 576px) and (max-width: 767.98px) { /* الهواتف الكبيرة */ }
@media (min-width: 768px) and (max-width: 991.98px) { /* الأجهزة اللوحية */ }
@media (min-width: 992px) and (max-width: 1199.98px) { /* سطح المكتب */ }
@media (min-width: 1200px) { /* الشاشات الكبيرة */ }
```

## 2. حلول الجداول المتجاوبة المتقدمة / Advanced Responsive Table Solutions

### أ. الحل الأول: التمرير الأفقي مع العمود الثابت

```css
/* حاوي الجدول المتجاوب */
.responsive-table-container {
  position: relative;
  overflow-x: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* الجدول الأساسي */
.responsive-table {
  width: 100%;
  min-width: 800px; /* الحد الأدنى للعرض */
  border-collapse: collapse;
  direction: rtl;
  text-align: right;
}

/* رأس الجدول */
.responsive-table thead th {
  background: #f9fafb;
  padding: 12px 16px;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* خلايا الجدول */
.responsive-table tbody td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

/* العمود الثابت (عادة عمود الإجراءات) */
.sticky-column {
  position: sticky;
  right: 0;
  background: white;
  z-index: 5;
  border-left: 2px solid #e5e7eb;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

.sticky-column.header {
  z-index: 15;
  background: #f9fafb;
}

/* تحسين التمرير على الأجهزة اللمسية */
.responsive-table-container {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.responsive-table-container::-webkit-scrollbar {
  height: 8px;
}

.responsive-table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.responsive-table-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}
```

### ب. الحل الثاني: إخفاء الأعمدة حسب الأولوية

```css
/* تصنيف الأعمدة حسب الأولوية */
.column-critical { /* دائماً مرئية */ }
.column-important { /* مخفية على الهواتف الصغيرة */ }
.column-optional { /* مخفية على الهواتف والأجهزة اللوحية */ }
.column-extra { /* مرئية فقط على الشاشات الكبيرة */ }

/* إخفاء الأعمدة تدريجياً */
@media (max-width: 575.98px) {
  .column-important,
  .column-optional,
  .column-extra {
    display: none;
  }
  
  /* إظهار مؤشر للأعمدة المخفية */
  .hidden-columns-indicator {
    display: inline-block;
    background: #3b82f6;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 8px;
  }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .column-optional,
  .column-extra {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .column-extra {
    display: none;
  }
}

/* زر إظهار/إخفاء الأعمدة */
.column-toggle-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 16px;
}

.column-toggle-btn:hover {
  background: #e5e7eb;
}
```

### ج. الحل الثالث: تحويل إلى تخطيط البطاقات

```css
/* تخطيط البطاقات للهواتف */
@media (max-width: 767.98px) {
  .responsive-table,
  .responsive-table thead,
  .responsive-table tbody,
  .responsive-table th,
  .responsive-table td,
  .responsive-table tr {
    display: block;
  }
  
  /* إخفاء رأس الجدول */
  .responsive-table thead tr {
    position: absolute;
    top: -9999px;
    right: -9999px;
  }
  
  /* تنسيق الصفوف كبطاقات */
  .responsive-table tbody tr {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
  }
  
  /* تنسيق الخلايا */
  .responsive-table tbody td {
    border: none;
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f3f4f6;
  }
  
  .responsive-table tbody td:last-child {
    border-bottom: none;
  }
  
  /* إضافة تسميات للبيانات */
  .responsive-table tbody td:before {
    content: attr(data-label);
    font-weight: 600;
    color: #374151;
    flex-shrink: 0;
    margin-left: 16px;
  }
  
  /* تنسيق البيانات */
  .responsive-table tbody td .cell-content {
    text-align: left;
    flex-grow: 1;
  }
  
  /* أزرار الإجراءات في البطاقات */
  .card-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f3f4f6;
  }
  
  .card-actions .btn {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    text-decoration: none;
  }
}
```

## 3. نظام الشبكة المتجاوب / Responsive Grid System

```css
/* نظام الشبكة الأساسي */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.col {
  flex: 1;
  padding: 0 8px;
  min-width: 0;
}

/* أعمدة بأحجام محددة */
.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* أعمدة متجاوبة */
@media (max-width: 767.98px) {
  .col-sm-12 { flex: 0 0 100%; }
  .col-sm-6 { flex: 0 0 50%; }
  .col-sm-4 { flex: 0 0 33.333333%; }
}

@media (min-width: 768px) {
  .col-md-12 { flex: 0 0 100%; }
  .col-md-6 { flex: 0 0 50%; }
  .col-md-4 { flex: 0 0 33.333333%; }
  .col-md-3 { flex: 0 0 25%; }
}
```

## 4. مكونات واجهة المستخدم المتجاوبة / Responsive UI Components

### أ. النماذج المتجاوبة

```css
/* حاوي النموذج */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* مجموعة الحقول */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

/* الحقول */
.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
  direction: rtl;
  text-align: right;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* النماذج المتجاوبة */
@media (max-width: 767.98px) {
  .form-container {
    padding: 16px;
    margin: 0 16px;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-row .form-group {
    margin-bottom: 16px;
  }
}

@media (min-width: 768px) {
  .form-row {
    display: flex;
    gap: 16px;
  }
  
  .form-row .form-group {
    flex: 1;
  }
}
```

### ب. البطاقات المتجاوبة

```css
/* البطاقة الأساسية */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 16px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

/* شبكة البطاقات */
.cards-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 767.98px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .card-body {
    padding: 16px;
  }
}
```

### ج. التنقل المتجاوب

```css
/* شريط التنقل الرئيسي */
.navbar {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  text-decoration: none;
}

.navbar-brand img {
  height: 32px;
  width: auto;
}

/* القائمة الجانبية */
.sidebar {
  width: 280px;
  background: #1f2937;
  color: white;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 1001;
  overflow-y: auto;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #374151;
}

.sidebar-menu {
  padding: 20px 0;
}

.sidebar-menu a {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #d1d5db;
  text-decoration: none;
  transition: background-color 0.2s;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
  background: #374151;
  color: white;
}

/* زر القائمة للهواتف */
.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
}

@media (max-width: 991.98px) {
  .menu-toggle {
    display: block;
  }
  
  .sidebar {
    width: 100%;
    max-width: 320px;
  }
}

/* تراكب الخلفية */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}
```

## 5. تحسينات الأداء للأجهزة المحمولة / Mobile Performance Optimizations

```css
/* تحسين الخطوط */
@font-face {
  font-family: 'IBM Plex Sans Arabic';
  src: url('/fonts/IBMPlexSansArabic-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* تحسين الصور */
.responsive-image {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

/* تحسين الرسوم المتحركة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* تحسين اللمس */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* منع التكبير على iOS */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  font-size: 16px;
}
```

## 6. أمثلة تطبيقية / Practical Examples

### مثال: جدول المشاريع المتجاوب

```html
<div class="responsive-table-container">
  <table class="responsive-table">
    <thead>
      <tr>
        <th class="sticky-column header">الإجراءات</th>
        <th class="column-critical">اسم المشروع</th>
        <th class="column-critical">العميل</th>
        <th class="column-important">الحالة</th>
        <th class="column-important">تاريخ البداية</th>
        <th class="column-optional">المدير</th>
        <th class="column-optional">الميزانية</th>
        <th class="column-extra">التقدم</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="sticky-column" data-label="الإجراءات">
          <div class="card-actions">
            <a href="#" class="btn btn-primary">تعديل</a>
            <a href="#" class="btn btn-secondary">عرض</a>
          </div>
        </td>
        <td class="column-critical" data-label="اسم المشروع">
          <div class="cell-content">موقع الشركة الجديد</div>
        </td>
        <td class="column-critical" data-label="العميل">
          <div class="cell-content">شركة التقنية المتقدمة</div>
        </td>
        <td class="column-important" data-label="الحالة">
          <div class="cell-content">
            <span class="badge badge-success">قيد التنفيذ</span>
          </div>
        </td>
        <td class="column-important" data-label="تاريخ البداية">
          <div class="cell-content">2024/01/15</div>
        </td>
        <td class="column-optional" data-label="المدير">
          <div class="cell-content">أحمد محمد</div>
        </td>
        <td class="column-optional" data-label="الميزانية">
          <div class="cell-content">50,000 ج.م</div>
        </td>
        <td class="column-extra" data-label="التقدم">
          <div class="cell-content">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 65%"></div>
            </div>
            <span>65%</span>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

---

*دليل التصميم المتجاوب لنظام متبرمج ERP - محسن للأجهزة المحمولة والتجربة العربية*
