var VueReactivity=function(t){"use strict";function e(t,e){const n=Object.create(null),r=t.split(",");for(let s=0;s<r.length;s++)n[r[s]]=!0;return e?t=>!!n[t.toLowerCase()]:t=>!!n[t]}const n={},r=()=>{},s=Object.assign,i=Object.prototype.hasOwnProperty,o=(t,e)=>i.call(t,e),c=Array.isArray,u=t=>"[object Map]"===_(t),a=t=>"function"==typeof t,l=t=>"symbol"==typeof t,f=t=>null!==t&&"object"==typeof t,h=Object.prototype.toString,_=t=>h.call(t),d=t=>"string"==typeof t&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,v=(t,e)=>t!==e&&(t==t||e==e),g=new WeakMap,p=[];let y;const w=Symbol(""),R=Symbol("");function b(t,e=n){(function(t){return t&&!0===t._isEffect})(t)&&(t=t.raw);const r=function(t,e){const n=function(){if(!n.active)return t();if(!p.includes(n)){E(n);try{return O(),p.push(n),y=n,t()}finally{p.pop(),P(),y=p[p.length-1]}}};return n.id=k++,n.allowRecurse=!!e.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=t,n.deps=[],n.options=e,n}(t,e);return e.lazy||r(),r}let k=0;function E(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}let m=!0;const S=[];function j(){S.push(m),m=!1}function O(){S.push(m),m=!0}function P(){const t=S.pop();m=void 0===t||t}function M(t,e,n){if(!m||void 0===y)return;let r=g.get(t);r||g.set(t,r=new Map);let s=r.get(n);s||r.set(n,s=new Set),s.has(y)||(s.add(y),y.deps.push(s))}function x(t,e,n,r,s,i){const o=g.get(t);if(!o)return;const a=new Set,l=t=>{t&&t.forEach((t=>{(t!==y||t.allowRecurse)&&a.add(t)}))};if("clear"===e)o.forEach(l);else if("length"===n&&c(t))o.forEach(((t,e)=>{("length"===e||e>=r)&&l(t)}));else switch(void 0!==n&&l(o.get(n)),e){case"add":c(t)?d(n)&&l(o.get("length")):(l(o.get(w)),u(t)&&l(o.get(R)));break;case"delete":c(t)||(l(o.get(w)),u(t)&&l(o.get(R)));break;case"set":u(t)&&l(o.get(w))}a.forEach((t=>{t.options.scheduler?t.options.scheduler(t):t()}))}const z=e("__proto__,__v_isRef,__isVue"),W=new Set(Object.getOwnPropertyNames(Symbol).map((t=>Symbol[t])).filter(l)),A=B(),T=B(!1,!0),V=B(!0),N=B(!0,!0),I=K();function K(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const n=St(this);for(let e=0,s=this.length;e<s;e++)M(n,0,e+"");const r=n[e](...t);return-1===r||!1===r?n[e](...t.map(St)):r}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){j();const n=St(this)[e].apply(this,t);return P(),n}})),t}function B(t=!1,e=!1){return function(n,r,s){if("__v_isReactive"===r)return!t;if("__v_isReadonly"===r)return t;if("__v_raw"===r&&s===(t?e?yt:pt:e?gt:vt).get(n))return n;const i=c(n);if(!t&&i&&o(I,r))return Reflect.get(I,r,s);const u=Reflect.get(n,r,s);if(l(r)?W.has(r):z(r))return u;if(t||M(n,0,r),e)return u;if(Ot(u)){return!i||!d(r)?u.value:u}return f(u)?t?bt(u):Rt(u):u}}function C(t=!1){return function(e,n,r,s){let i=e[n];if(!t&&(r=St(r),i=St(i),!c(e)&&Ot(i)&&!Ot(r)))return i.value=r,!0;const u=c(e)&&d(n)?Number(n)<e.length:o(e,n),a=Reflect.set(e,n,r,s);return e===St(s)&&(u?v(r,i)&&x(e,"set",n,r):x(e,"add",n,r)),a}}const L={get:A,set:C(),deleteProperty:function(t,e){const n=o(t,e),r=Reflect.deleteProperty(t,e);return r&&n&&x(t,"delete",e,void 0),r},has:function(t,e){const n=Reflect.has(t,e);return l(e)&&W.has(e)||M(t,0,e),n},ownKeys:function(t){return M(t,0,c(t)?"length":w),Reflect.ownKeys(t)}},Y={get:V,set:(t,e)=>!0,deleteProperty:(t,e)=>!0},q=s({},L,{get:T,set:C(!0)}),D=s({},Y,{get:N}),F=t=>f(t)?Rt(t):t,G=t=>f(t)?bt(t):t,H=t=>t,J=t=>Reflect.getPrototypeOf(t);function Q(t,e,n=!1,r=!1){const s=St(t=t.__v_raw),i=St(e);e!==i&&!n&&M(s,0,e),!n&&M(s,0,i);const{has:o}=J(s),c=r?H:n?G:F;return o.call(s,e)?c(t.get(e)):o.call(s,i)?c(t.get(i)):void(t!==s&&t.get(e))}function U(t,e=!1){const n=this.__v_raw,r=St(n),s=St(t);return t!==s&&!e&&M(r,0,t),!e&&M(r,0,s),t===s?n.has(t):n.has(t)||n.has(s)}function X(t,e=!1){return t=t.__v_raw,!e&&M(St(t),0,w),Reflect.get(t,"size",t)}function Z(t){t=St(t);const e=St(this);return J(e).has.call(e,t)||(e.add(t),x(e,"add",t,t)),this}function $(t,e){e=St(e);const n=St(this),{has:r,get:s}=J(n);let i=r.call(n,t);i||(t=St(t),i=r.call(n,t));const o=s.call(n,t);return n.set(t,e),i?v(e,o)&&x(n,"set",t,e):x(n,"add",t,e),this}function tt(t){const e=St(this),{has:n,get:r}=J(e);let s=n.call(e,t);s||(t=St(t),s=n.call(e,t)),r&&r.call(e,t);const i=e.delete(t);return s&&x(e,"delete",t,void 0),i}function et(){const t=St(this),e=0!==t.size,n=t.clear();return e&&x(t,"clear",void 0,void 0),n}function nt(t,e){return function(n,r){const s=this,i=s.__v_raw,o=St(i),c=e?H:t?G:F;return!t&&M(o,0,w),i.forEach(((t,e)=>n.call(r,c(t),c(e),s)))}}function rt(t,e,n){return function(...r){const s=this.__v_raw,i=St(s),o=u(i),c="entries"===t||t===Symbol.iterator&&o,a="keys"===t&&o,l=s[t](...r),f=n?H:e?G:F;return!e&&M(i,0,a?R:w),{next(){const{value:t,done:e}=l.next();return e?{value:t,done:e}:{value:c?[f(t[0]),f(t[1])]:f(t),done:e}},[Symbol.iterator](){return this}}}}function st(t){return function(...e){return"delete"!==t&&this}}function it(){const t={get(t){return Q(this,t)},get size(){return X(this)},has:U,add:Z,set:$,delete:tt,clear:et,forEach:nt(!1,!1)},e={get(t){return Q(this,t,!1,!0)},get size(){return X(this)},has:U,add:Z,set:$,delete:tt,clear:et,forEach:nt(!1,!0)},n={get(t){return Q(this,t,!0)},get size(){return X(this,!0)},has(t){return U.call(this,t,!0)},add:st("add"),set:st("set"),delete:st("delete"),clear:st("clear"),forEach:nt(!0,!1)},r={get(t){return Q(this,t,!0,!0)},get size(){return X(this,!0)},has(t){return U.call(this,t,!0)},add:st("add"),set:st("set"),delete:st("delete"),clear:st("clear"),forEach:nt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((s=>{t[s]=rt(s,!1,!1),n[s]=rt(s,!0,!1),e[s]=rt(s,!1,!0),r[s]=rt(s,!0,!0)})),[t,n,e,r]}const[ot,ct,ut,at]=it();function lt(t,e){const n=e?t?at:ut:t?ct:ot;return(e,r,s)=>"__v_isReactive"===r?!t:"__v_isReadonly"===r?t:"__v_raw"===r?e:Reflect.get(o(n,r)&&r in e?n:e,r,s)}const ft={get:lt(!1,!1)},ht={get:lt(!1,!0)},_t={get:lt(!0,!1)},dt={get:lt(!0,!0)},vt=new WeakMap,gt=new WeakMap,pt=new WeakMap,yt=new WeakMap;function wt(t){return t.__v_skip||!Object.isExtensible(t)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((t=>_(t).slice(8,-1))(t))}function Rt(t){return t&&t.__v_isReadonly?t:kt(t,!1,L,ft,vt)}function bt(t){return kt(t,!0,Y,_t,pt)}function kt(t,e,n,r,s){if(!f(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const i=s.get(t);if(i)return i;const o=wt(t);if(0===o)return t;const c=new Proxy(t,2===o?r:n);return s.set(t,c),c}function Et(t){return mt(t)?Et(t.__v_raw):!(!t||!t.__v_isReactive)}function mt(t){return!(!t||!t.__v_isReadonly)}function St(t){return t&&St(t.__v_raw)||t}const jt=t=>f(t)?Rt(t):t;function Ot(t){return Boolean(t&&!0===t.__v_isRef)}class Pt{constructor(t,e=!1){this._shallow=e,this.__v_isRef=!0,this._rawValue=e?t:St(t),this._value=e?t:jt(t)}get value(){return M(St(this),0,"value"),this._value}set value(t){t=this._shallow?t:St(t),v(t,this._rawValue)&&(this._rawValue=t,this._value=this._shallow?t:jt(t),x(St(this),"set","value",t))}}function Mt(t,e=!1){return Ot(t)?t:new Pt(t,e)}function xt(t){return Ot(t)?t.value:t}const zt={get:(t,e,n)=>xt(Reflect.get(t,e,n)),set:(t,e,n,r)=>{const s=t[e];return Ot(s)&&!Ot(n)?(s.value=n,!0):Reflect.set(t,e,n,r)}};class Wt{constructor(t){this.__v_isRef=!0;const{get:e,set:n}=t((()=>M(this,0,"value")),(()=>x(this,"set","value")));this._get=e,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}class At{constructor(t,e){this._object=t,this._key=e,this.__v_isRef=!0}get value(){return this._object[this._key]}set value(t){this._object[this._key]=t}}function Tt(t,e){return Ot(t[e])?t[e]:new At(t,e)}class Vt{constructor(t,e,n){this._setter=e,this._dirty=!0,this.__v_isRef=!0,this.effect=b(t,{lazy:!0,scheduler:()=>{this._dirty||(this._dirty=!0,x(St(this),"set","value"))}}),this.__v_isReadonly=n}get value(){const t=St(this);return t._dirty&&(t._value=this.effect(),t._dirty=!1),M(t,0,"value"),t._value}set value(t){this._setter(t)}}return t.ITERATE_KEY=w,t.computed=function(t){let e,n;return a(t)?(e=t,n=r):(e=t.get,n=t.set),new Vt(e,n,a(t)||!t.set)},t.customRef=function(t){return new Wt(t)},t.effect=b,t.enableTracking=O,t.isProxy=function(t){return Et(t)||mt(t)},t.isReactive=Et,t.isReadonly=mt,t.isRef=Ot,t.markRaw=function(t){return((t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})})(t,"__v_skip",!0),t},t.pauseTracking=j,t.proxyRefs=function(t){return Et(t)?t:new Proxy(t,zt)},t.reactive=Rt,t.readonly=bt,t.ref=function(t){return Mt(t)},t.resetTracking=P,t.shallowReactive=function(t){return kt(t,!1,q,ht,gt)},t.shallowReadonly=function(t){return kt(t,!0,D,dt,yt)},t.shallowRef=function(t){return Mt(t,!0)},t.stop=function(t){t.active&&(E(t),t.options.onStop&&t.options.onStop(),t.active=!1)},t.toRaw=St,t.toRef=Tt,t.toRefs=function(t){const e=c(t)?new Array(t.length):{};for(const n in t)e[n]=Tt(t,n);return e},t.track=M,t.trigger=x,t.triggerRef=function(t){x(St(t),"set","value",void 0)},t.unref=xt,Object.defineProperty(t,"__esModule",{value:!0}),t}({});
