function t(t,e){const n=Object.create(null),r=t.split(",");for(let s=0;s<r.length;s++)n[r[s]]=!0;return e?t=>!!n[t.toLowerCase()]:t=>!!n[t]}const e={},n=()=>{},r=Object.assign,s=Object.prototype.hasOwnProperty,i=(t,e)=>s.call(t,e),o=Array.isArray,c=t=>"[object Map]"===h(t),u=t=>"function"==typeof t,a=t=>"symbol"==typeof t,l=t=>null!==t&&"object"==typeof t,f=Object.prototype.toString,h=t=>f.call(t),_=t=>"string"==typeof t&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,d=(t,e)=>t!==e&&(t==t||e==e),v=new WeakMap,g=[];let p;const y=Symbol(""),w=Symbol("");function R(t,n=e){(function(t){return t&&!0===t._isEffect})(t)&&(t=t.raw);const r=function(t,e){const n=function(){if(!n.active)return t();if(!g.includes(n)){E(n);try{return O(),g.push(n),p=n,t()}finally{g.pop(),M(),p=g[g.length-1]}}};return n.id=k++,n.allowRecurse=!!e.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=t,n.deps=[],n.options=e,n}(t,n);return n.lazy||r(),r}function b(t){t.active&&(E(t),t.options.onStop&&t.options.onStop(),t.active=!1)}let k=0;function E(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}let S=!0;const j=[];function m(){j.push(S),S=!1}function O(){j.push(S),S=!0}function M(){const t=j.pop();S=void 0===t||t}function P(t,e,n){if(!S||void 0===p)return;let r=v.get(t);r||v.set(t,r=new Map);let s=r.get(n);s||r.set(n,s=new Set),s.has(p)||(s.add(p),p.deps.push(s))}function x(t,e,n,r,s,i){const u=v.get(t);if(!u)return;const a=new Set,l=t=>{t&&t.forEach((t=>{(t!==p||t.allowRecurse)&&a.add(t)}))};if("clear"===e)u.forEach(l);else if("length"===n&&o(t))u.forEach(((t,e)=>{("length"===e||e>=r)&&l(t)}));else switch(void 0!==n&&l(u.get(n)),e){case"add":o(t)?_(n)&&l(u.get("length")):(l(u.get(y)),c(t)&&l(u.get(w)));break;case"delete":o(t)||(l(u.get(y)),c(t)&&l(u.get(w)));break;case"set":c(t)&&l(u.get(y))}a.forEach((t=>{t.options.scheduler?t.options.scheduler(t):t()}))}const z=t("__proto__,__v_isRef,__isVue"),W=new Set(Object.getOwnPropertyNames(Symbol).map((t=>Symbol[t])).filter(a)),A=C(),N=C(!1,!0),V=C(!0),I=C(!0,!0),K=B();function B(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const n=Mt(this);for(let e=0,s=this.length;e<s;e++)P(n,0,e+"");const r=n[e](...t);return-1===r||!1===r?n[e](...t.map(Mt)):r}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){m();const n=Mt(this)[e].apply(this,t);return M(),n}})),t}function C(t=!1,e=!1){return function(n,r,s){if("__v_isReactive"===r)return!t;if("__v_isReadonly"===r)return t;if("__v_raw"===r&&s===(t?e?yt:pt:e?gt:vt).get(n))return n;const c=o(n);if(!t&&c&&i(K,r))return Reflect.get(K,r,s);const u=Reflect.get(n,r,s);if(a(r)?W.has(r):z(r))return u;if(t||P(n,0,r),e)return u;if(zt(u)){return!c||!_(r)?u.value:u}return l(u)?t?kt(u):Rt(u):u}}function L(t=!1){return function(e,n,r,s){let c=e[n];if(!t&&(r=Mt(r),c=Mt(c),!o(e)&&zt(c)&&!zt(r)))return c.value=r,!0;const u=o(e)&&_(n)?Number(n)<e.length:i(e,n),a=Reflect.set(e,n,r,s);return e===Mt(s)&&(u?d(r,c)&&x(e,"set",n,r):x(e,"add",n,r)),a}}const q={get:A,set:L(),deleteProperty:function(t,e){const n=i(t,e),r=Reflect.deleteProperty(t,e);return r&&n&&x(t,"delete",e,void 0),r},has:function(t,e){const n=Reflect.has(t,e);return a(e)&&W.has(e)||P(t,0,e),n},ownKeys:function(t){return P(t,0,o(t)?"length":y),Reflect.ownKeys(t)}},D={get:V,set:(t,e)=>!0,deleteProperty:(t,e)=>!0},F=r({},q,{get:N,set:L(!0)}),G=r({},D,{get:I}),H=t=>l(t)?Rt(t):t,J=t=>l(t)?kt(t):t,Q=t=>t,T=t=>Reflect.getPrototypeOf(t);function U(t,e,n=!1,r=!1){const s=Mt(t=t.__v_raw),i=Mt(e);e!==i&&!n&&P(s,0,e),!n&&P(s,0,i);const{has:o}=T(s),c=r?Q:n?J:H;return o.call(s,e)?c(t.get(e)):o.call(s,i)?c(t.get(i)):void(t!==s&&t.get(e))}function X(t,e=!1){const n=this.__v_raw,r=Mt(n),s=Mt(t);return t!==s&&!e&&P(r,0,t),!e&&P(r,0,s),t===s?n.has(t):n.has(t)||n.has(s)}function Y(t,e=!1){return t=t.__v_raw,!e&&P(Mt(t),0,y),Reflect.get(t,"size",t)}function Z(t){t=Mt(t);const e=Mt(this);return T(e).has.call(e,t)||(e.add(t),x(e,"add",t,t)),this}function $(t,e){e=Mt(e);const n=Mt(this),{has:r,get:s}=T(n);let i=r.call(n,t);i||(t=Mt(t),i=r.call(n,t));const o=s.call(n,t);return n.set(t,e),i?d(e,o)&&x(n,"set",t,e):x(n,"add",t,e),this}function tt(t){const e=Mt(this),{has:n,get:r}=T(e);let s=n.call(e,t);s||(t=Mt(t),s=n.call(e,t)),r&&r.call(e,t);const i=e.delete(t);return s&&x(e,"delete",t,void 0),i}function et(){const t=Mt(this),e=0!==t.size,n=t.clear();return e&&x(t,"clear",void 0,void 0),n}function nt(t,e){return function(n,r){const s=this,i=s.__v_raw,o=Mt(i),c=e?Q:t?J:H;return!t&&P(o,0,y),i.forEach(((t,e)=>n.call(r,c(t),c(e),s)))}}function rt(t,e,n){return function(...r){const s=this.__v_raw,i=Mt(s),o=c(i),u="entries"===t||t===Symbol.iterator&&o,a="keys"===t&&o,l=s[t](...r),f=n?Q:e?J:H;return!e&&P(i,0,a?w:y),{next(){const{value:t,done:e}=l.next();return e?{value:t,done:e}:{value:u?[f(t[0]),f(t[1])]:f(t),done:e}},[Symbol.iterator](){return this}}}}function st(t){return function(...e){return"delete"!==t&&this}}function it(){const t={get(t){return U(this,t)},get size(){return Y(this)},has:X,add:Z,set:$,delete:tt,clear:et,forEach:nt(!1,!1)},e={get(t){return U(this,t,!1,!0)},get size(){return Y(this)},has:X,add:Z,set:$,delete:tt,clear:et,forEach:nt(!1,!0)},n={get(t){return U(this,t,!0)},get size(){return Y(this,!0)},has(t){return X.call(this,t,!0)},add:st("add"),set:st("set"),delete:st("delete"),clear:st("clear"),forEach:nt(!0,!1)},r={get(t){return U(this,t,!0,!0)},get size(){return Y(this,!0)},has(t){return X.call(this,t,!0)},add:st("add"),set:st("set"),delete:st("delete"),clear:st("clear"),forEach:nt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((s=>{t[s]=rt(s,!1,!1),n[s]=rt(s,!0,!1),e[s]=rt(s,!1,!0),r[s]=rt(s,!0,!0)})),[t,n,e,r]}const[ot,ct,ut,at]=it();function lt(t,e){const n=e?t?at:ut:t?ct:ot;return(e,r,s)=>"__v_isReactive"===r?!t:"__v_isReadonly"===r?t:"__v_raw"===r?e:Reflect.get(i(n,r)&&r in e?n:e,r,s)}const ft={get:lt(!1,!1)},ht={get:lt(!1,!0)},_t={get:lt(!0,!1)},dt={get:lt(!0,!0)},vt=new WeakMap,gt=new WeakMap,pt=new WeakMap,yt=new WeakMap;function wt(t){return t.__v_skip||!Object.isExtensible(t)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((t=>h(t).slice(8,-1))(t))}function Rt(t){return t&&t.__v_isReadonly?t:St(t,!1,q,ft,vt)}function bt(t){return St(t,!1,F,ht,gt)}function kt(t){return St(t,!0,D,_t,pt)}function Et(t){return St(t,!0,G,dt,yt)}function St(t,e,n,r,s){if(!l(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const i=s.get(t);if(i)return i;const o=wt(t);if(0===o)return t;const c=new Proxy(t,2===o?r:n);return s.set(t,c),c}function jt(t){return mt(t)?jt(t.__v_raw):!(!t||!t.__v_isReactive)}function mt(t){return!(!t||!t.__v_isReadonly)}function Ot(t){return jt(t)||mt(t)}function Mt(t){return t&&Mt(t.__v_raw)||t}function Pt(t){return((t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})})(t,"__v_skip",!0),t}const xt=t=>l(t)?Rt(t):t;function zt(t){return Boolean(t&&!0===t.__v_isRef)}function Wt(t){return Vt(t)}function At(t){return Vt(t,!0)}class Nt{constructor(t,e=!1){this._shallow=e,this.__v_isRef=!0,this._rawValue=e?t:Mt(t),this._value=e?t:xt(t)}get value(){return P(Mt(this),0,"value"),this._value}set value(t){t=this._shallow?t:Mt(t),d(t,this._rawValue)&&(this._rawValue=t,this._value=this._shallow?t:xt(t),x(Mt(this),"set","value",t))}}function Vt(t,e=!1){return zt(t)?t:new Nt(t,e)}function It(t){x(Mt(t),"set","value",void 0)}function Kt(t){return zt(t)?t.value:t}const Bt={get:(t,e,n)=>Kt(Reflect.get(t,e,n)),set:(t,e,n,r)=>{const s=t[e];return zt(s)&&!zt(n)?(s.value=n,!0):Reflect.set(t,e,n,r)}};function Ct(t){return jt(t)?t:new Proxy(t,Bt)}class Lt{constructor(t){this.__v_isRef=!0;const{get:e,set:n}=t((()=>P(this,0,"value")),(()=>x(this,"set","value")));this._get=e,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}function qt(t){return new Lt(t)}function Dt(t){const e=o(t)?new Array(t.length):{};for(const n in t)e[n]=Gt(t,n);return e}class Ft{constructor(t,e){this._object=t,this._key=e,this.__v_isRef=!0}get value(){return this._object[this._key]}set value(t){this._object[this._key]=t}}function Gt(t,e){return zt(t[e])?t[e]:new Ft(t,e)}class Ht{constructor(t,e,n){this._setter=e,this._dirty=!0,this.__v_isRef=!0,this.effect=R(t,{lazy:!0,scheduler:()=>{this._dirty||(this._dirty=!0,x(Mt(this),"set","value"))}}),this.__v_isReadonly=n}get value(){const t=Mt(this);return t._dirty&&(t._value=this.effect(),t._dirty=!1),P(t,0,"value"),t._value}set value(t){this._setter(t)}}function Jt(t){let e,r;return u(t)?(e=t,r=n):(e=t.get,r=t.set),new Ht(e,r,u(t)||!t.set)}export{y as ITERATE_KEY,Jt as computed,qt as customRef,R as effect,O as enableTracking,Ot as isProxy,jt as isReactive,mt as isReadonly,zt as isRef,Pt as markRaw,m as pauseTracking,Ct as proxyRefs,Rt as reactive,kt as readonly,Wt as ref,M as resetTracking,bt as shallowReactive,Et as shallowReadonly,At as shallowRef,b as stop,Mt as toRaw,Gt as toRef,Dt as toRefs,P as track,x as trigger,It as triggerRef,Kt as unref};
