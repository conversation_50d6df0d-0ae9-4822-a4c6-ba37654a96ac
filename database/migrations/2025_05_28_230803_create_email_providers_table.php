<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_providers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('service_name'); // Gmail, Outlook, cPanel, etc.
            $table->text('admin_credentials'); // Will be encrypted
            $table->text('configuration')->nullable(); // SMTP settings, etc.
            $table->string('primary_email');
            $table->integer('email_accounts_count')->default(1);
            $table->decimal('monthly_cost', 8, 2)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_providers');
    }
};
