<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->enum('status', ['planning', 'in_progress', 'testing', 'completed', 'on_hold', 'cancelled'])->default('planning');
            $table->decimal('budget', 12, 2);
            $table->string('live_link')->nullable();
            $table->string('whatsapp_group')->nullable();
            $table->string('github_repo')->nullable();
            $table->integer('progress_percentage')->default(0);
            $table->json('milestones')->nullable(); // Store milestones as JSON
            $table->string('project_type')->nullable(); // web, mobile, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
