<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hosting_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('provider_name');
            $table->text('server_credentials'); // Will be encrypted
            $table->text('control_panel_access'); // Will be encrypted
            $table->string('server_ip')->nullable();
            $table->string('hosting_type'); // shared, vps, dedicated, cloud
            $table->decimal('monthly_cost', 8, 2)->nullable();
            $table->date('renewal_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hosting_details');
    }
};
