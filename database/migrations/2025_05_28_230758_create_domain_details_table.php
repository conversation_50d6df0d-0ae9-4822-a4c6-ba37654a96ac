<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('domain_name');
            $table->string('registrar');
            $table->text('dns_settings')->nullable();
            $table->date('registration_date');
            $table->date('renewal_date');
            $table->decimal('annual_cost', 8, 2)->nullable();
            $table->boolean('auto_renewal')->default(false);
            $table->text('registrar_credentials'); // Will be encrypted
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_details');
    }
};
